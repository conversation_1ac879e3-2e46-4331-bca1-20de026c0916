/**
 * Custom String Compression Module
 * 
 * This module provides string compression functionality to replace the built-in
 * stringCompression option from js-confuser. It uses LZ-String compression
 * to compress repeated string literals and generates decompression code.
 */

const LZString = require('lz-string');
const { generateRandomName } = require('./utils');

/**
 * Configuration options for string compression
 */
const DEFAULT_STRING_COMPRESSION_OPTIONS = {
    enabled: true,
    minStringLength: 3,
    stringDelimiter: '',
    compressionThreshold: 0.8, // Only compress if it saves at least 20%
    skipPatterns: [
        /^(import|export|require)\s/,  // Module imports/exports
        /^https?:\/\//,                // URLs
        /^data:/,                      // Data URLs
        /^\w+:\/\//                    // Other protocols
    ]
};

/**
 * String compression class that handles the compression logic
 */
class StringCompressor {
    constructor(options = {}) {
        this.options = { ...DEFAULT_STRING_COMPRESSION_OPTIONS, ...options };
        this.stringMap = new Map();
        this.compressedStrings = [];
        this.functionName = generateRandomName();
        this.arrayName = generateRandomName();
        this.libraryName = generateRandomName();
    }

    /**
     * Check if a string should be compressed
     * @param {string} str - The string to check
     * @returns {boolean} Whether the string should be compressed
     */
    shouldCompressString(str) {
        if (!this.options.enabled) return false;
        if (str.length < this.options.minStringLength) return false;
        if (str.includes(this.options.stringDelimiter)) return false;
        
        // Check skip patterns
        for (const pattern of this.options.skipPatterns) {
            if (pattern.test(str)) return false;
        }
        
        return true;
    }

    /**
     * Add a string to the compression map
     * @param {string} str - The string to add
     * @returns {number|null} The index of the string, or null if not added
     */
    addString(str) {
        if (!this.shouldCompressString(str)) return null;
        
        if (!this.stringMap.has(str)) {
            const index = this.stringMap.size;
            this.stringMap.set(str, index);
            this.compressedStrings.push(str);
            return index;
        }
        
        return this.stringMap.get(str);
    }

    /**
     * Generate the compressed string payload
     * @returns {string} The compressed string data
     */
    generateCompressedPayload() {
        if (this.compressedStrings.length === 0) return '';
        
        const payload = this.compressedStrings.join(this.options.stringDelimiter);
        return LZString.compressToUTF16(payload);
    }

    /**
     * Generate the decompression function code
     * @returns {string} The JavaScript code for string decompression
     */
    generateDecompressionCode() {
        if (this.compressedStrings.length === 0) return '';

        const compressedPayload = this.generateCompressedPayload();

        // Generate random variable names for all identifiers
        const randomNames = {
            decompressFunc: generateRandomName(),
            compressed: generateRandomName(),
            i: generateRandomName(),
            dictionary: generateRandomName(),
            enlargeIn: generateRandomName(),
            dictSize: generateRandomName(),
            numBits: generateRandomName(),
            entry: generateRandomName(),
            result: generateRandomName(),
            bits: generateRandomName(),
            maxpower: generateRandomName(),
            power: generateRandomName(),
            c: generateRandomName(),
            f: generateRandomName(),
            data: generateRandomName(),
            val: generateRandomName(),
            position: generateRandomName(),
            index: generateRandomName(),
            resb: generateRandomName(),
            w: generateRandomName(),
            decompressed: generateRandomName()
        };

        // Obfuscated decompression template with all random names
        const decompressionCode = `
var ${this.functionName};
(function() {
    var ${randomNames.compressed} = "${compressedPayload}";
    var ${this.arrayName};

    function ${randomNames.decompressFunc}(${randomNames.compressed}) {
        if (${randomNames.compressed} == null) return "";
        if (${randomNames.compressed} == "") return null;

        var ${randomNames.i}, ${randomNames.dictionary} = [], ${randomNames.enlargeIn} = 4, ${randomNames.dictSize} = 4, ${randomNames.numBits} = 3, ${randomNames.entry} = "", ${randomNames.result} = [],
            ${randomNames.bits}, ${randomNames.maxpower}, ${randomNames.power}, ${randomNames.c}, ${randomNames.f} = String.fromCharCode, ${randomNames.data} = {
                ${randomNames.val}: ${randomNames.compressed}.charCodeAt(0) - 32,
                ${randomNames.position}: 16384,
                ${randomNames.index}: 1
            };

        for (${randomNames.i} = 0; ${randomNames.i} < 3; ${randomNames.i}++) ${randomNames.dictionary}[${randomNames.i}] = ${randomNames.i};

        ${randomNames.bits} = 0; ${randomNames.maxpower} = 4; ${randomNames.power} = 1;
        while (${randomNames.power} != ${randomNames.maxpower}) {
            var ${randomNames.resb} = ${randomNames.data}.${randomNames.val} & ${randomNames.data}.${randomNames.position};
            ${randomNames.data}.${randomNames.position} >>= 1;
            if (${randomNames.data}.${randomNames.position} == 0) {
                ${randomNames.data}.${randomNames.position} = 16384;
                ${randomNames.data}.${randomNames.val} = ${randomNames.compressed}.charCodeAt(${randomNames.data}.${randomNames.index}++) - 32;
            }
            ${randomNames.bits} |= (${randomNames.resb} > 0 ? 1 : 0) * ${randomNames.power};
            ${randomNames.power} <<= 1;
        }

        switch (${randomNames.bits}) {
            case 0:
                ${randomNames.bits} = 0; ${randomNames.maxpower} = 256; ${randomNames.power} = 1;
                while (${randomNames.power} != ${randomNames.maxpower}) {
                    ${randomNames.resb} = ${randomNames.data}.${randomNames.val} & ${randomNames.data}.${randomNames.position};
                    ${randomNames.data}.${randomNames.position} >>= 1;
                    if (${randomNames.data}.${randomNames.position} == 0) {
                        ${randomNames.data}.${randomNames.position} = 16384;
                        ${randomNames.data}.${randomNames.val} = ${randomNames.compressed}.charCodeAt(${randomNames.data}.${randomNames.index}++) - 32;
                    }
                    ${randomNames.bits} |= (${randomNames.resb} > 0 ? 1 : 0) * ${randomNames.power};
                    ${randomNames.power} <<= 1;
                }
                ${randomNames.c} = ${randomNames.f}(${randomNames.bits});
                break;
            case 1:
                ${randomNames.bits} = 0; ${randomNames.maxpower} = 65536; ${randomNames.power} = 1;
                while (${randomNames.power} != ${randomNames.maxpower}) {
                    ${randomNames.resb} = ${randomNames.data}.${randomNames.val} & ${randomNames.data}.${randomNames.position};
                    ${randomNames.data}.${randomNames.position} >>= 1;
                    if (${randomNames.data}.${randomNames.position} == 0) {
                        ${randomNames.data}.${randomNames.position} = 16384;
                        ${randomNames.data}.${randomNames.val} = ${randomNames.compressed}.charCodeAt(${randomNames.data}.${randomNames.index}++) - 32;
                    }
                    ${randomNames.bits} |= (${randomNames.resb} > 0 ? 1 : 0) * ${randomNames.power};
                    ${randomNames.power} <<= 1;
                }
                ${randomNames.c} = ${randomNames.f}(${randomNames.bits});
                break;
            case 2:
                return "";
        }

        ${randomNames.dictionary}[3] = ${randomNames.c};
        var ${randomNames.w} = ${randomNames.c};
        ${randomNames.result}.push(${randomNames.c});

        while (true) {
            if (${randomNames.data}.${randomNames.index} > ${randomNames.compressed}.length) return "";

            ${randomNames.bits} = 0; ${randomNames.maxpower} = Math.pow(2, ${randomNames.numBits}); ${randomNames.power} = 1;
            while (${randomNames.power} != ${randomNames.maxpower}) {
                ${randomNames.resb} = ${randomNames.data}.${randomNames.val} & ${randomNames.data}.${randomNames.position};
                ${randomNames.data}.${randomNames.position} >>= 1;
                if (${randomNames.data}.${randomNames.position} == 0) {
                    ${randomNames.data}.${randomNames.position} = 16384;
                    ${randomNames.data}.${randomNames.val} = ${randomNames.compressed}.charCodeAt(${randomNames.data}.${randomNames.index}++) - 32;
                }
                ${randomNames.bits} |= (${randomNames.resb} > 0 ? 1 : 0) * ${randomNames.power};
                ${randomNames.power} <<= 1;
            }

            switch (${randomNames.c} = ${randomNames.bits}) {
                case 0:
                    ${randomNames.bits} = 0; ${randomNames.maxpower} = 256; ${randomNames.power} = 1;
                    while (${randomNames.power} != ${randomNames.maxpower}) {
                        ${randomNames.resb} = ${randomNames.data}.${randomNames.val} & ${randomNames.data}.${randomNames.position};
                        ${randomNames.data}.${randomNames.position} >>= 1;
                        if (${randomNames.data}.${randomNames.position} == 0) {
                            ${randomNames.data}.${randomNames.position} = 16384;
                            ${randomNames.data}.${randomNames.val} = ${randomNames.compressed}.charCodeAt(${randomNames.data}.${randomNames.index}++) - 32;
                        }
                        ${randomNames.bits} |= (${randomNames.resb} > 0 ? 1 : 0) * ${randomNames.power};
                        ${randomNames.power} <<= 1;
                    }
                    ${randomNames.dictionary}[${randomNames.dictSize}++] = ${randomNames.f}(${randomNames.bits});
                    ${randomNames.c} = ${randomNames.dictSize} - 1;
                    ${randomNames.enlargeIn}--;
                    break;
                case 1:
                    ${randomNames.bits} = 0; ${randomNames.maxpower} = 65536; ${randomNames.power} = 1;
                    while (${randomNames.power} != ${randomNames.maxpower}) {
                        ${randomNames.resb} = ${randomNames.data}.${randomNames.val} & ${randomNames.data}.${randomNames.position};
                        ${randomNames.data}.${randomNames.position} >>= 1;
                        if (${randomNames.data}.${randomNames.position} == 0) {
                            ${randomNames.data}.${randomNames.position} = 16384;
                            ${randomNames.data}.${randomNames.val} = ${randomNames.compressed}.charCodeAt(${randomNames.data}.${randomNames.index}++) - 32;
                        }
                        ${randomNames.bits} |= (${randomNames.resb} > 0 ? 1 : 0) * ${randomNames.power};
                        ${randomNames.power} <<= 1;
                    }
                    ${randomNames.dictionary}[${randomNames.dictSize}++] = ${randomNames.f}(${randomNames.bits});
                    ${randomNames.c} = ${randomNames.dictSize} - 1;
                    ${randomNames.enlargeIn}--;
                    break;
                case 2:
                    return ${randomNames.result}.join('');
            }

            if (${randomNames.enlargeIn} == 0) {
                ${randomNames.enlargeIn} = Math.pow(2, ${randomNames.numBits});
                ${randomNames.numBits}++;
            }

            if (${randomNames.dictionary}[${randomNames.c}]) {
                ${randomNames.entry} = ${randomNames.dictionary}[${randomNames.c}];
            } else {
                if (${randomNames.c} === ${randomNames.dictSize}) {
                    ${randomNames.entry} = ${randomNames.w} + ${randomNames.w}.charAt(0);
                } else {
                    return null;
                }
            }

            ${randomNames.result}.push(${randomNames.entry});
            ${randomNames.dictionary}[${randomNames.dictSize}++] = ${randomNames.w} + ${randomNames.entry}.charAt(0);
            ${randomNames.enlargeIn}--;
            ${randomNames.w} = ${randomNames.entry};

            if (${randomNames.enlargeIn} == 0) {
                ${randomNames.enlargeIn} = Math.pow(2, ${randomNames.numBits});
                ${randomNames.numBits}++;
            }
        }
    }

    var ${randomNames.decompressed} = ${randomNames.decompressFunc}(${randomNames.compressed});
    ${this.arrayName} = ${randomNames.decompressed}.split("${this.options.stringDelimiter}");

    ${this.functionName} = function(${randomNames.index}) {
        return ${this.arrayName}[${randomNames.index}];
    };
})();
`;

        return decompressionCode.trim();
    }



    /**
     * Get replacement code for a string literal
     * @param {number} index - The index of the string in the compressed array
     * @returns {string} The replacement function call
     */
    getStringReplacement(index) {
        return `${this.functionName}(${index})`;
    }

    /**
     * Get statistics about the compression
     * @returns {Object} Compression statistics
     */
    getStats() {
        const originalSize = this.compressedStrings.reduce((sum, str) => sum + str.length, 0);
        const compressedSize = this.generateCompressedPayload().length;
        
        return {
            totalStrings: this.compressedStrings.length,
            originalSize: originalSize,
            compressedSize: compressedSize,
            compressionRatio: originalSize > 0 ? compressedSize / originalSize : 1,
            spaceSaved: originalSize - compressedSize
        };
    }

    /**
     * Reset the compressor state
     */
    reset() {
        this.stringMap.clear();
        this.compressedStrings = [];
        this.functionName = generateRandomName();
        this.arrayName = generateRandomName();
        this.libraryName = generateRandomName();
    }
}

/**
 * Advanced string literal extraction using AST parsing
 * @param {string} code - JavaScript code to analyze
 * @returns {Array} Array of string literal information
 */
function extractStringLiterals(code) {
    const babel = require('@babel/parser');
    const traverse = require('@babel/traverse').default;

    try {
        // Parse code into AST with comprehensive options
        const ast = babel.parse(code, {
            sourceType: 'module',
            allowImportExportEverywhere: true,
            allowReturnOutsideFunction: true,
            plugins: [
                'jsx',
                'typescript',
                'decorators-legacy',
                'classProperties',
                'objectRestSpread',
                'functionBind',
                'exportDefaultFrom',
                'exportNamespaceFrom',
                'dynamicImport',
                'nullishCoalescingOperator',
                'optionalChaining',
                'bigInt',
                'optionalCatchBinding',
                'throwExpressions',
                'topLevelAwait'
            ],
            strictMode: false,
            errorRecovery: true
        });

        const stringLiterals = [];

        // Traverse AST to find string literals
        traverse(ast, {
            StringLiteral(path) {
                // Skip import/export statements
                if (isImportExportContext(path)) return;

                // Skip require calls
                if (isRequireContext(path)) return;

                // Skip property keys in object expressions
                if (isObjectPropertyKey(path)) return;

                // Get source location for replacement
                const start = path.node.start;
                const end = path.node.end;
                const value = path.node.value;
                const raw = code.substring(start, end);

                stringLiterals.push({
                    value: value,
                    raw: raw,
                    start: start,
                    end: end,
                    path: path
                });
            },
            TemplateLiteral(path) {
                // Handle template literals without expressions
                if (path.node.expressions.length === 0) {
                    const value = path.node.quasis[0].value.cooked;
                    const start = path.node.start;
                    const end = path.node.end;
                    const raw = code.substring(start, end);

                    stringLiterals.push({
                        value: value,
                        raw: raw,
                        start: start,
                        end: end,
                        path: path,
                        isTemplate: true
                    });
                }
            }
        });

        return stringLiterals.sort((a, b) => b.start - a.start); // Reverse order for safe replacement

    } catch (error) {
        // Fallback to regex if AST parsing fails
        return extractStringLiteralsRegex(code);
    }
}

/**
 * Fallback regex-based string extraction
 * @param {string} code - JavaScript code
 * @returns {Array} Array of string literal information
 */
function extractStringLiteralsRegex(code) {
    const stringRegex = /(['"`])((?:(?!\1)[^\\]|\\.)*)(\1)/g;
    const literals = [];
    let match;

    while ((match = stringRegex.exec(code)) !== null) {
        const fullMatch = match[0];
        const content = match[2];
        const actualString = content.replace(/\\(.)/g, '$1');

        literals.push({
            value: actualString,
            raw: fullMatch,
            start: match.index,
            end: match.index + fullMatch.length
        });
    }

    return literals.sort((a, b) => b.start - a.start);
}

/**
 * Check if string literal is in import/export context
 */
function isImportExportContext(path) {
    return path.findParent(parent =>
        parent.isImportDeclaration() ||
        parent.isExportDeclaration() ||
        parent.isImportExpression()
    );
}

/**
 * Check if string literal is in require() context
 */
function isRequireContext(path) {
    const parent = path.parent;
    return parent &&
           parent.type === 'CallExpression' &&
           parent.callee &&
           parent.callee.name === 'require' &&
           parent.arguments[0] === path.node;
}

/**
 * Check if string literal is an object property key
 */
function isObjectPropertyKey(path) {
    const parent = path.parent;
    return parent && parent.type === 'ObjectProperty' && parent.key === path.node;
}

/**
 * Process JavaScript code and apply string compression
 * @param {string} code - The JavaScript code to process
 * @param {Object} options - Compression options
 * @returns {Object} Result with compressed code and metadata
 */
function compressStrings(code, options = {}) {
    const compressor = new StringCompressor(options);

    try {
        // Advanced AST-based string extraction
        const stringLiterals = extractStringLiterals(code);
        let processedCode = code;
        const replacements = [];

        // Process each string literal
        for (const literal of stringLiterals) {
            const index = compressor.addString(literal.value);
            if (index !== null) {
                replacements.push({
                    original: literal.raw,
                    replacement: compressor.getStringReplacement(index),
                    start: literal.start,
                    end: literal.end
                });
            }
        }

        // Apply replacements (already sorted in reverse order)
        for (const replacement of replacements) {
            const before = processedCode.substring(0, replacement.start);
            const after = processedCode.substring(replacement.end);
            processedCode = before + replacement.replacement + after;
        }
        
        // Generate decompression code
        const decompressionCode = compressor.generateDecompressionCode();
        
        // Prepend decompression code if we have compressed strings
        if (decompressionCode) {
            processedCode = decompressionCode + '\n\n' + processedCode;
        }
        
        const stats = compressor.getStats();
        
        return {
            code: processedCode,
            success: true,
            stats: stats,
            stringsCompressed: stats.totalStrings
        };
        
    } catch (error) {
        return {
            code: code,
            success: false,
            error: error.message,
            stats: { totalStrings: 0, originalSize: 0, compressedSize: 0, compressionRatio: 1 },
            stringsCompressed: 0
        };
    }
}

module.exports = {
    StringCompressor,
    compressStrings,
    DEFAULT_STRING_COMPRESSION_OPTIONS
};
