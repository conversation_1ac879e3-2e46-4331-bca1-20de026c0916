/**
 * Test script for order.js integration with string compression
 */

const { executeProcessesInOrder, getProcessesInOrder } = require('./src/order');

// Test code with strings that should be compressed
const testCode = `
function testFunction() {
    console.log("Hello, world!");
    console.log("Welcome to the application");
    console.log("Hello, world!"); // Duplicate
    
    const message = "Welcome to the application"; // Duplicate
    const greeting = "Hello, world!"; // Duplicate
    
    return greeting + " - " + message;
}

testFunction();
`;

console.log('=== Order.js Integration Test with String Compression ===\n');

console.log('Original code:');
console.log(testCode);
console.log(`Original size: ${Buffer.byteLength(testCode, 'utf8')} bytes\n`);

// Show the process order
console.log('=== Process Execution Order ===');
const processes = getProcessesInOrder();
processes.forEach((process, index) => {
    console.log(`${index + 1}. [Order ${process.order}] ${process.name}`);
    console.log(`   Description: ${process.description}`);
    console.log(`   Reason: ${process.reason}`);
    console.log(`   Enabled: ${process.enabled ? '✅' : '❌'}`);
    console.log('');
});

// Execute all processes in order
console.log('=== Executing All Processes ===');
executeProcessesInOrder(testCode, (event, data) => {
    if (event.endsWith('_start')) {
        console.log(`🚀 Starting: ${data.processName}`);
        console.log(`   Input size: ${data.codeSize} bytes`);
    } else if (event.endsWith('_success')) {
        console.log(`✅ Completed: ${data.processName}`);
        console.log(`   Duration: ${data.duration}ms`);

        // Handle cases where obfuscatedSize and compressionRatio might not be available
        if (data.obfuscatedSize !== undefined) {
            console.log(`   Output size: ${data.obfuscatedSize} bytes`);
        }
        if (data.compressionRatio !== undefined) {
            console.log(`   Compression ratio: ${data.compressionRatio.toFixed(3)}`);
        }

        // Show process-specific details
        if (data.simplifiedCount !== undefined) {
            console.log(`   Transformations: ${data.simplifiedCount}`);
        }
        if (data.foundGlobals && data.foundGlobals.length > 0) {
            console.log(`   Global identifiers found: ${data.foundGlobals.length}`);
        }

        // Show string compression details if available
        if (data.stringCompression) {
            console.log(`   String compression: ${data.stringCompression.enabled ? '✅' : '❌'}`);
            if (data.stringCompression.enabled) {
                console.log(`   Strings compressed: ${data.stringCompression.stringsCompressed}`);
                console.log(`   String stats:`, data.stringCompression.stats);
            }
        }
        console.log('');
    } else if (event.endsWith('_error')) {
        console.log(`❌ Failed: ${data.processName}`);
        console.log(`   Error: ${data.error}`);
        console.log('');
    }
}).then(result => {
    console.log('=== Final Results ===');
    console.log('✅ All processes completed successfully!');
    console.log(`📦 Final code size: ${Buffer.byteLength(result.code, 'utf8')} bytes`);
    console.log(`🔄 Total processes: ${result.totalProcesses}`);
    console.log(`✅ Successful processes: ${result.successfulProcesses}`);
    
    console.log('\n=== Process Summary ===');
    result.executionDetails.forEach((detail, index) => {
        console.log(`${index + 1}. ${detail.processName}: ${detail.success ? '✅' : '❌'} (${detail.duration}ms)`);
        if (detail.stringCompression && detail.stringCompression.enabled) {
            console.log(`   └─ Compressed ${detail.stringCompression.stringsCompressed} strings`);
        }
    });
    
    console.log('\n--- Final Obfuscated Code Preview (first 500 chars) ---');
    console.log(result.code.substring(0, 500) + '...\n');
    
    // Test execution
    console.log('=== Testing Final Code Execution ===');
    try {
        eval(result.code);
        console.log('✅ Final code executed successfully!');
    } catch (error) {
        console.log('❌ Final code execution failed:', error.message);
    }
    
}).catch(error => {
    console.error('❌ Process execution failed:', error);
});
